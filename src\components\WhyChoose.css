.feature-section {
  padding: 4rem 0;
  position: relative;
}

.feature-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, #020617, #0f172a);
  z-index: -10;
}

.feature-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.feature-header {
  text-align: center;
  margin-bottom: 4rem;
}

.feature-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.feature-highlight {
  color: #00aeff;
}

.feature-subtitle {
  color: #ccc;
  max-width: 600px;
  margin: 0 auto;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background-color: rgba(30, 35, 40, 0.5);
  border: 1px solid #1f2937;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: justify;
  transition: all 0.3s ease;
  backdrop-filter: blur(6px);
}

.feature-card:hover {
  border-color: rgba(0, 174, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 174, 255, 0.15);
}

.feature-icon {
  width: 24px;
  height: 24px;
  color: #00aeff;
}

.feature-icon-wrapper {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #fff;
  text-align: center;
}

.feature-card-description {
  color: #999;
  font-size: 0.9rem;
}

.feature-trusted-box {
  background-color: rgba(30, 35, 40, 0.7);
  border: 1px solid #1f2937;
  border-radius: 1rem;
  margin-top: 4rem;
  padding: 2rem;
  backdrop-filter: blur(6px);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.feature-trusted-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .feature-trusted-content {
    flex-direction: row;
    align-items: center;
  }
}

.feature-trusted-text {
  flex: 3;
}

.feature-trusted-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #fff;
}

.feature-trusted-description {
  color: #ccc;
  margin-bottom: 1rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list-item {
  display: flex;
  align-items: start;
  gap: 0.5rem;
  color: #ccc;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}

.feature-check {
  color: #00aeff;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.feature-summary {
  margin-top: 4rem;
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  background-color: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(4px);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid #334155;
}

@media (min-width: 768px) {
  .feature-summary {
    padding: 2rem;
  }
}

.feature-summary-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

@media (min-width: 768px) {
  .feature-summary-content {
    flex-direction: row;
  }
}

.feature-summary-text {
  width: 100%;
}

@media (min-width: 768px) {
  .feature-summary-text {
    width: 60%;
  }
}

.feature-summary-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
}

.feature-summary-desc {
  color: #d1d5db;
  margin-bottom: 1rem;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-list-item {
  display: flex;
  align-items: flex-start;
  color: #d1d5db;
}

.feature-check-icon {
  color: #38bdf8;
  margin-right: 0.5rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.feature-rating-box {
  width: 100%;
  height: 12rem;
  background-color: #0f172a;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  .feature-rating-box {
    height: auto;
    width: 40%;
  }
}

.feature-rating-score {
  text-align: center;
}

.feature-rating-percent {
  font-size: 3rem;
  font-weight: 700;
  color: #38bdf8;
  margin-bottom: 0.5rem;
}

.feature-rating-label {
  font-size: 0.875rem;
  color: #9ca3af;
}

.feature-rating-stars {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
  gap: 0.25rem;
}

.feature-star {
  width: 1.25rem;
  height: 1.25rem;
  color: #0ea5e9;
  fill: currentColor;
}
