import React, { useState } from 'react';
import Button from './Button';
import { Send } from 'lucide-react';
import './RequestDemo.css';

const RequestDemo: React.FC = () => {
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    organization: '',
    requestType: 'demo'
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formState);
    setIsSubmitted(true);
  };

  return (
    <section id="request-demo" className="subscribe-section">
      <div className="subscribe-bg-overlay">
        <div className="subscribe-bg-dark"></div>
        <div className="subscribe-bg-lines"></div>
      </div>

      <div className="subscribe-container">
        <div className="subscribe-inner">
          <div className="subscribe-grid">
            <div className="subscribe-info">
              <h2 className="subscribe-heading">
                Ready to <span className="subscribe-highlight">Upgrade</span> Your Security?
              </h2>
              <p className="subscribe-description">
                Request a personalized demo to see how RapidResponse can transform your security operations and emergency response capabilities.
              </p>
              <div className="subscribe-features">
                <h3 className="subscribe-feature-title">What you'll get:</h3>
                <ul className="subscribe-feature-list">
                  {[
                    'Personalized system demonstration',
                    'Integration assessment for your infrastructure',
                    'Customized pricing proposal',
                    'Free trial period option'
                  ].map((item, index) => (
                    <li key={index} className="subscribe-feature-item">
                      <span className="subscribe-feature-icon">{index + 1}</span>
                      <span className="subscribe-feature-text">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="subscribe-form-wrapper">
              {!isSubmitted ? (
                <form onSubmit={handleSubmit}>
                  <div className="subscribe-form-group">
                    <label htmlFor="name" className="subscribe-label">Full Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formState.name}
                      onChange={handleChange}
                      className="subscribe-input"
                      required
                    />
                  </div>

                  <div className="subscribe-form-group">
                    <label htmlFor="email" className="subscribe-label">Email Address</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formState.email}
                      onChange={handleChange}
                      className="subscribe-input"
                      required
                    />
                  </div>

                  <div className="subscribe-form-group">
                    <label htmlFor="organization" className="subscribe-label">Organization</label>
                    <input
                      type="text"
                      id="organization"
                      name="organization"
                      value={formState.organization}
                      onChange={handleChange}
                      className="subscribe-input"
                      required
                    />
                  </div>

                  <div className="subscribe-form-group">
                    <label htmlFor="requestType" className="subscribe-label">Request Type</label>
                    <select
                      id="requestType"
                      name="requestType"
                      value={formState.requestType}
                      onChange={handleChange}
                      className="subscribe-input"
                    >
                      <option value="demo">Product Demo</option>
                      <option value="pricing">Pricing Information</option>
                      <option value="technical">Technical Specifications</option>
                      <option value="consultation">Security Consultation</option>
                    </select>
                  </div>

                  <Button type="submit" className="subscribe-submit-button">
                    <Send size={18} className="mr-2" />
                    Request Your Demo
                  </Button>
                </form>
              ) : (
                <div className="subscribe-thankyou">
                  <div className="subscribe-icon">
                    <Send size={32} className="subscribe-icon-graphic" />
                  </div>
                  <h3 className="subscribe-thankyou-title">Request Submitted!</h3>
                  <p className="subscribe-thankyou-text">
                    Thank you for your interest in RapidResponse. Our team will contact you within 24 hours to schedule your personalized demo.
                  </p>
                  <Button 
                    variant="outline" 
                    className="subscribe-reset-button"
                    onClick={() => setIsSubmitted(false)}
                  >
                    Submit Another Request
                  </Button>
                </div>
              )}
            </div>

          </div>
        </div>
      </div>
    </section>
  );
};

export default RequestDemo;