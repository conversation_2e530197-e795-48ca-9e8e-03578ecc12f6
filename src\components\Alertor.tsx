import React from 'react';
import './Alertor.css';
import StatusGreen from './assets/green.png';

const Alertor: React.FC = () => {

  return (
    <section id="how-it-works" className="key-section">

      <div className="hero-radar-wrapper">
        <div className="hero-radar-circle hero-radar-delay-0"></div>
        <div className="hero-radar-circle hero-radar-delay-1"></div>
        <div className="hero-radar-circle hero-radar-delay-2"></div>
      </div>

      <div className="key-overlay"></div>
    
      <div className="donut safe">
        <div className="donut-default"></div>
        <div className="donut-line"></div>
          <div className="donut-text">
            <span>100%</span>
          </div>
        <div className="donut-case"></div>
      </div>

      <div className="status">
          <img src={StatusGreen} alt="Status Green" />
          <p>Very Secure</p>
      </div>
    
    </section>
  );
};

export default Alertor;