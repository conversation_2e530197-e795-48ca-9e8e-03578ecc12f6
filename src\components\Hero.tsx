import React from 'react';
import { Alert<PERSON>riangle, BarChart2} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Button from './Button';
import './Hero.css';
import PublicIcon from './assets/public.jpg';
import SecurityIcon from './assets/security.jpg';
import AdminIcon from './assets/admin.jpg';

const Hero: React.FC = () => {
  const navigate = useNavigate();

  return (
    <section className="hero-section">
      <div className="detection-wrapper">
        <div className="detection-radar">
          <div className="detection-sweep"></div>
          <div className="detection-ping"></div>
        </div>
      </div>
      
      <div className="hero-grid-bg"></div>

      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-badge">
            <AlertTriangle size={16} className="hero-badge-icon" />
            React, Resist, Re<PERSON>in Safe
          </div>

          <h1 className="hero-heading">
            RAPIDRESPONSE,
            <br />
            <span className="hero-highlight">To Protect Your Safety.</span>
          </h1>

          <p className="hero-subheading">
            It all starts here, 
            to get a real-time gunshot alert.
          </p>

      <div className="role-select-container">
        <div className="role-select_box">
          {/* Public */}
          <div
            className="role-select_item"
            onClick={() => navigate('/Public')}
            style={{ cursor: 'pointer' }}
          >
            <div className="role-select-item_link">
              <div className="role-select-item_bg"></div>
              <img src={PublicIcon} alt="Public Icon" />
              <div className="role-select-item_title">Public</div>
            </div>
          </div>

          {/* Security Personnel */}
          <div
            className="role-select_item"
            onClick={() => navigate('/Security')}
            style={{ cursor: 'pointer' }}
          >
            <div className="role-select-item_link">
              <div className="role-select-item_bg"></div>
              <img src={SecurityIcon} alt="Security Icon" />
              <div className="role-select-item_title">Security Personnel</div>
            </div>
          </div>

          {/* Admin */}
          <div
            className="role-select_item"
            onClick={() => navigate('/Admin')}
            style={{ cursor: 'pointer' }}
          >
            <div className="role-select-item_link">
              <div className="role-select-item_bg"></div>
              <img src={AdminIcon} alt="Admin Icon" />
              <div className="role-select-item_title">Admin</div>
            </div>
          </div>
        </div>
      </div>

          <div className="hero-stats">
            <div className="hero-stat">
              <p className="hero-stat-value">
                <span className="hero-stat-icon"><BarChart2 size={22} /></span>97%
              </p>
              <p className="hero-stat-label">Detection Accuracy</p>
            </div>
            <div className="hero-stat">
              <p className="hero-stat-value">&lt;1s</p>
              <p className="hero-stat-label">Response Time</p>
            </div>
            <div className="hero-stat">
              <p className="hero-stat-value">24/7</p>
              <p className="hero-stat-label">Monitoring</p>
            </div>
            <div className="hero-stat">
              <p className="hero-stat-value">500m</p>
              <p className="hero-stat-label">Detection Range</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
