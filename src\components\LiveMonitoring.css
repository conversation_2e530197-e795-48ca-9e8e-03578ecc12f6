.live-monitoring {
  padding: 4rem 0;
  color: #ffffff;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .monitoring-grid {
    grid-template-columns: 3fr 2fr;
  }
}

.map-panel, .alert-panel {
  background-color: #1e293b;
  border: 1px solid #334155;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
  height: 400px;
  display: flex;
  flex-direction: column;
}

.map-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #334155;
  padding: 0.5rem 1rem;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.alert-header{
  display: flex;
  align-items: center;
  background-color: #334155;
  padding: 0.5rem 1rem;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.map-title{
  display: flex;
  align-items: center;
  padding: 3px;
  border: 1px solid grey;
  border-radius: 5px;
}

.map-title img {
  width: 16px;
  height: auto;
}

.map-time {
  display: flex;
  align-items: center;
}

.alert-type {
  display: flex;
  align-items: center;
}

.icon {
  color: #60a5fa;
  margin-right: 0.5rem;
}

.alert-list icon{
      color: #ed0000;
}

.map-body {
  position: relative;
  flex: 1;
}

.map-background {
  position: absolute;
  inset: 0;
  opacity: 0.2;
  background-image:
    linear-gradient(#1e88e5 0.5px, transparent 0.5px),
    linear-gradient(to right, #1e88e5 0.5px, transparent 0.5px);
  background-size: 20px 20px;
}

.map-marker {
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.map-marker.high {
  background-color: #f87171;
}

.map-marker.medium {
  background-color: #facc15;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.5); opacity: 0.5; }
}

.map-overlay {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

.alert-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-item {
  padding: 0.75rem;
  border-left: 4px solid;
  border-radius: 0.5rem;
}

.alert-item.high {
  background-color: rgba(248, 113, 113, 0.1);
  border-color: #f60000;
}

.alert-item.medium {
  background-color: rgba(250, 204, 21, 0.1);
  border-color: #fcca00;
}

.alert-top {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 0.5rem;
}

.timestamp {
  font-size: 0.75rem;
  color: #94a3b8;
}

.alert-location {
  font-size: 0.875rem;
  color: #e2e8f0;
  margin-bottom: 0.5rem;
}

.alert-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-priority.high {
  background-color: rgba(248, 113, 113, 0.2);
  color: #f87171;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.alert-priority.medium {
  background-color: rgba(250, 204, 21, 0.2);
  color: #facc15;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.alert-link {
  font-size: 0.75rem;
  color: #60a5fa;
  background: none;
  border: none;
  cursor: pointer;
}

.alert-link:hover {
  color: #3b82f6;
}

.alert-actions {
  padding: 0.75rem 1rem;
  border-top: 1px solid #334155;
  display: flex;
  gap: 0.5rem;
}

.btn-primary {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  padding: 0.5rem 1rem;
  background-color: #334155;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: #475569;
}
