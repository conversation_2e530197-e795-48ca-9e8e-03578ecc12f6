import React from 'react';
import './Button.css';

interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  className?: string;
  onClick?: () => void;
  href?: string;
  type?: 'button' | 'submit' | 'reset';
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  className = '',
  onClick,
  href,
  type = 'button'
}) => {
  const baseClass = 'button-base';
  const variantClass = `button-${variant}`;
  const combinedClass = `${baseClass} ${variantClass} ${className}`;

  if (href) {
    return (
      <a href={href} className={combinedClass} onClick={onClick}>
        {children}
      </a>
    );
  }

  return (
    <button type={type} className={combinedClass} onClick={onClick}>
      {children}
    </button>
  );
};

export default Button;