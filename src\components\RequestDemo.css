/* RequestDemo.css */
#request-demo {
  padding-top: 4rem;
  padding-bottom: 6rem;
  position: relative;
  background-color: rgb(2, 6, 23);
}

.subscribe-bg-overlay {
  position: absolute;
  inset: 0;
  z-index: -10;
}

.subscribe-bg-dark {
  position: absolute;
  inset: 0;
  background-color: rgba(15, 23, 42, 0.9);
}

.subscribe-bg-lines {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  background-image: linear-gradient(0deg, #1e88e5 1px, transparent 1px), linear-gradient(90deg, #1e88e5 1px, transparent 1px);
  background-size: 40px 40px;
  background-position: -1px -1px;
}

.subscribe-container {
  position: relative;
  z-index: 10;
  padding-left: 1rem;
  padding-right: 1rem;
  max-width: 1280px;
  margin: 0 auto;
}

.subscribe-inner {
  width: 100%;
}

.subscribe-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .subscribe-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.subscribe-info {
  display: flex;
  flex-direction: column;
}

.subscribe-heading {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
  text-align:center;
}

.subscribe-highlight {
  color: #42a5f5;
}

.subscribe-description {
  color: #d1d5db;
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
  text-align:center;
}

.subscribe-features {
  background-color: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  border: 1px solid #334155;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.subscribe-feature-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

.subscribe-feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subscribe-feature-item {
  display: flex;
  align-items: flex-start;
  color: #d1d5db;
  font-size: 0.875rem;
}

.subscribe-feature-icon {
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 9999px;
  background-color: rgba(66, 165, 245, 0.2);
  color: #42a5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.subscribe-feature-text {
  padding-top: 0.25rem;
}

.subscribe-form-wrapper {
  background-color: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(4px);
  border: 1px solid #334155;
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.subscribe-form-group {
  margin-bottom: 1.25rem;
}

.subscribe-label {
  display: block;
  color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.subscribe-input {
  width: 95%;
  padding: 0.625rem 1rem;
  background-color: rgba(15, 23, 42, 0.8);
  border: 1px solid #475569;
  border-radius: 0.375rem;
  color: #fff;
  outline: none;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.subscribe-input:focus {
  border-color: #42a5f5;
  box-shadow: 0 0 0 2px rgba(66, 165, 245, 0.3);
}

.subscribe-submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background-color: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.subscribe-submit-button:hover {
  background-color: #2563eb;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

.subscribe-thankyou {
  text-align: center;
  padding: 1.5rem 0;
}

.subscribe-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1.5rem;
  background-color: rgba(66, 165, 245, 0.2);
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.subscribe-icon-graphic {
  color: #42a5f5;
}

.subscribe-thankyou-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #ffffff;
}

.subscribe-thankyou-text {
  color: #d1d5db;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.subscribe-reset-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(51, 65, 85, 0.8);
  color: #ffffff;
  border: 1px solid #475569;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.subscribe-reset-button:hover {
  background-color: rgba(71, 85, 105, 0.8);
  border-color: #60a5fa;
}
