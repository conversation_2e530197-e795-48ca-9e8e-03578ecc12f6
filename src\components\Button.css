.button-base {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  user-select: none;
}

/* Primary */
.button-primary {
  background-color: #3b82f6;
  color: white;
  animation: glow 2s ease-in-out infinite alternate;
}

.button-primary:hover {
  background-color: #2563eb;
}

/* Glow animation */
@keyframes glow {
  from {
    box-shadow: 0 0 5px #3b82f6;
  }
  to {
    box-shadow: 0 0 20px #2563eb;
  }
}

/* Secondary */
.button-secondary {
  background-color: #ef4444;
  color: white;
}

.button-secondary:hover {
  background-color: #dc2626;
}

/* Outline */
.button-outline {
  background-color: transparent;
  border: 1px solid #3b82f6;
  color: #3b82f6;
}

.button-outline:hover {
  background-color: rgba(59, 130, 246, 0.1);
}
